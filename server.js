const express = require('express');
const multer = require('multer');
const ffmpeg = require('fluent-ffmpeg');
const path = require('path');
const fs = require('fs');

const app = express();
const upload = multer({ dest: 'uploads/' }); // Temporary storage for uploaded files

// Serve static files (HTML, CSS, JS)
app.use(express.static('public'));

// Route for file upload and conversion
app.post('/convert', upload.single('audio'), (req, res) => {
    if (!req.file) {
        return res.status(400).send('No file uploaded.');
    }

    const inputFilePath = req.file.path;
    const originalName = req.file.originalname;
    const fileNameWithoutExt = path.parse(originalName).name; // Get original filename without extension
    const outputFormat = req.body.format || 'mp3'; // Default to MP3 if no format is specified
    const quality = req.body.quality || '320'; // Default to 320 kbps if no quality is specified
    const outputFilePath = path.join(__dirname, 'uploads', `${fileNameWithoutExt}.${outputFormat}`);

    // Convert the audio file
    let conversion = ffmpeg(inputFilePath)
        .toFormat(outputFormat); // Set the output format dynamically

    // Apply quality settings for compressed formats
    const compressedFormats = ['mp3', 'aac', 'ogg', 'm4a', 'wma', 'opus'];
    if (compressedFormats.includes(outputFormat)) {
        conversion = conversion.audioBitrate(quality + 'k');
    }

    conversion.on('end', () => {
            res.download(outputFilePath, `${fileNameWithoutExt}.${outputFormat}`, (err) => {
                if (err) {
                    console.error('Error downloading file:', err);
                    res.status(500).send('Error downloading file.');
                }
                // Clean up uploaded and converted files
                fs.unlinkSync(inputFilePath);
                fs.unlinkSync(outputFilePath);
            });
        })
        .on('error', (err) => {
            console.error('Error during conversion:', err);
            res.status(500).send('Error during conversion.');
        })
        .save(outputFilePath);
});

// Start the server
const PORT = process.env.PORT || 3000;
app.listen(PORT, () => {
    console.log(`Server is running on http://localhost:${PORT}`);
});
