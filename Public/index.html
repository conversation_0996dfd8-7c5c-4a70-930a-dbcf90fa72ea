<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Audio Convertor</title>
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Custom CSS -->
    <style>
        body {
            background: linear-gradient(135deg, #6a11cb, #2575fc);
            color: #fff;
            min-height: 100vh;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            margin: 0;
            padding: 20px;
        }
        .card {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 15px;
            padding: 2rem;
            width: 100%;
            max-width: 500px;
            margin: 0 auto;
        }
        .form-control, .form-select {
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            color: #fff;
        }
        .form-control:focus, .form-select:focus {
            background: rgba(255, 255, 255, 0.2);
            border-color: rgba(255, 255, 255, 0.4);
            color: #fff;
        }
        .btn-primary {
            background: #2575fc;
            border: none;
            transition: background 0.3s ease;
        }
        .btn-primary:hover {
            background: #6a11cb;
        }
        footer {
            margin-top: 2rem;
            text-align: center;
            font-size: 0.9rem;
            opacity: 0.8;
        }
        .donate-button {
            margin-top: 1.5rem;
            background: #28a745;
            border: none;
            padding: 0.75rem 1.5rem;
            border-radius: 50px;
            font-size: 1rem;
            color: #fff;
            cursor: pointer;
            transition: background 0.3s ease;
        }
        .donate-button:hover {
            background: #218838;
        }
        .language-toggle {
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 8px;
            color: #fff;
            padding: 0.5rem 1rem;
            cursor: pointer;
            transition: background 0.3s ease;
            font-size: 0.9rem;
        }
        .language-toggle:hover {
            background: rgba(255, 255, 255, 0.2);
        }
        .language-dropdown {
            position: relative;
            display: inline-block;
            margin-bottom: 1rem;
            text-align: right;
        }
        .dropdown-content {
            display: none;
            position: absolute;
            right: 0;
            background: rgba(255, 255, 255, 0.9);
            min-width: 120px;
            border-radius: 8px;
            box-shadow: 0px 8px 16px 0px rgba(0,0,0,0.2);
            z-index: 1;
        }
        .dropdown-content a {
            color: #333;
            padding: 8px 16px;
            text-decoration: none;
            display: block;
            border-radius: 8px;
        }
        .dropdown-content a:hover {
            background-color: rgba(0,0,0,0.1);
        }
        .show {
            display: block;
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- Language Toggle -->
        <div class="language-dropdown">
            <button class="language-toggle" onclick="toggleDropdown()">🌐 Language</button>
            <div id="languageDropdown" class="dropdown-content">
                <a href="#" onclick="changeLanguage('en')">English</a>
                <a href="#" onclick="changeLanguage('sn')">Shona</a>
                <a href="#" onclick="changeLanguage('es')">Español</a>
                <a href="#" onclick="changeLanguage('fr')">Français</a>
                <a href="#" onclick="changeLanguage('pt')">Português</a>
                <a href="#" onclick="changeLanguage('ja')">日本語</a>
                <a href="#" onclick="changeLanguage('sw')">Kiswahili</a>
                <a href="#" onclick="changeLanguage('zu')">isiZulu</a>
            </div>
        </div>

        <div class="card shadow-lg">
            <h1 class="text-center mb-4" data-en="🎵 Audio Convertor" data-sn="🎵 Chishandura Maungira" data-es="🎵 Conversor de Audio" data-fr="🎵 Convertisseur Audio" data-pt="🎵 Conversor de Áudio" data-ja="🎵 オーディオコンバーター" data-sw="🎵 Kibadilisha Sauti" data-zu="🎵 Isikhongolose Sezwi">🎵 Audio Convertor</h1>
            <form action="/convert" method="post" enctype="multipart/form-data">
                <div class="mb-3">
                    <label for="audio" class="form-label" data-en="Upload Audio File:" data-sn="Dziputurike Chipfimbikwa:" data-es="Subir Archivo de Audio:" data-fr="Télécharger Fichier Audio:" data-pt="Carregar Arquivo de Áudio:" data-ja="オーディオファイルをアップロード:" data-sw="Pakia Faili la Sauti:" data-zu="Layisha Ifayela Lezwi:">Upload Audio File:</label>
                    <input type="file" class="form-control" name="audio" id="audio" accept="audio/*" required>
                </div>
                <div class="mb-3">
                    <label for="format" class="form-label" data-en="Select Output Format:" data-sn="Keta Mhando Ichabuda:" data-es="Seleccionar Formato de Salida:" data-fr="Sélectionner Format de Sortie:" data-pt="Selecionar Formato de Saída:" data-ja="出力フォーマットを選択:" data-sw="Chagua Muundo wa Kutoka:" data-zu="Khetha Ifomathi Yokuphuma:">Select Output Format:</label>
                    <select class="form-select" name="format" id="format" onchange="toggleQualitySettings()">
                        <option value="mp3">MP3</option>
                        <option value="wav">WAV</option>
                        <option value="aac">AAC</option>
                        <option value="ogg">OGG</option>
                        <option value="flac">FLAC</option>
                        <option value="m4a">M4A</option>
                        <option value="aiff">AIFF</option>
                        <option value="wma">WMA</option>
                        <option value="opus">OPUS</option>
                    </select>
                </div>
                <div class="mb-3" id="qualitySettings">
                    <label for="quality" class="form-label" data-en="Audio Quality:" data-sn="Hunhu Hwezwi:" data-es="Calidad de Audio:" data-fr="Qualité Audio:" data-pt="Qualidade do Áudio:" data-ja="音質:" data-sw="Ubora wa Sauti:" data-zu="Ikhwalithi Yezwi:">Audio Quality:</label>
                    <select class="form-select" name="quality" id="quality">
                        <option value="128" data-en="128 kbps (Standard)" data-sn="128 kbps (Yakajairwa)" data-es="128 kbps (Estándar)" data-fr="128 kbps (Standard)" data-pt="128 kbps (Padrão)" data-ja="128 kbps (標準)" data-sw="128 kbps (Kawaida)" data-zu="128 kbps (Okujwayelekile)">128 kbps (Standard)</option>
                        <option value="192" data-en="192 kbps (Good)" data-sn="192 kbps (Zvakanaka)" data-es="192 kbps (Buena)" data-fr="192 kbps (Bonne)" data-pt="192 kbps (Boa)" data-ja="192 kbps (良好)" data-sw="192 kbps (Nzuri)" data-zu="192 kbps (Okuhle)">192 kbps (Good)</option>
                        <option value="256" data-en="256 kbps (High)" data-sn="256 kbps (Yakakwira)" data-es="256 kbps (Alta)" data-fr="256 kbps (Haute)" data-pt="256 kbps (Alta)" data-ja="256 kbps (高品質)" data-sw="256 kbps (Juu)" data-zu="256 kbps (Ephezulu)">256 kbps (High)</option>
                        <option value="320" selected data-en="320 kbps (Premium)" data-sn="320 kbps (Yepamusoro)" data-es="320 kbps (Premium)" data-fr="320 kbps (Premium)" data-pt="320 kbps (Premium)" data-ja="320 kbps (プレミアム)" data-sw="320 kbps (Bora)" data-zu="320 kbps (Okusezingeni)">320 kbps (Premium)</option>
                    </select>
                </div>
                <button type="submit" class="btn btn-primary w-100" data-en="Convert" data-sn="Shandura" data-es="Convertir" data-fr="Convertir" data-pt="Converter" data-ja="変換" data-sw="Badilisha" data-zu="Guqula">Convert</button>
            </form>
        </div>
    </div>

    <!-- Footer -->
    <footer data-en="Created By <strong>Anesu アネス Adrian Mupemhi</strong> for <strong>Benjamin Music Initiatives</strong><br>Copyright 2025" data-sn="Chakasikwa NA <strong>Anesu アネス Adrian Mupemhi</strong> We <strong>Benjamin Music Initiatives</strong><br>Chikatemwanyora 2025" data-es="Creado por <strong>Anesu アネス Adrian Mupemhi</strong> para <strong>Benjamin Music Initiatives</strong><br>Derechos de Autor 2025" data-fr="Créé par <strong>Anesu アネス Adrian Mupemhi</strong> pour <strong>Benjamin Music Initiatives</strong><br>Droits d'Auteur 2025" data-pt="Criado por <strong>Anesu アネス Adrian Mupemhi</strong> para <strong>Benjamin Music Initiatives</strong><br>Direitos Autorais 2025" data-ja="<strong>Anesu アネス Adrian Mupemhi</strong>が<strong>Benjamin Music Initiatives</strong>のために作成<br>著作権 2025" data-sw="Imeundwa na <strong>Anesu アネス Adrian Mupemhi</strong> kwa <strong>Benjamin Music Initiatives</strong><br>Hakimiliki 2025" data-zu="Kwenziwe ngu <strong>Anesu アネス Adrian Mupemhi</strong> we <strong>Benjamin Music Initiatives</strong><br>Ilungelo Lokushicilela 2025">
        Created By <strong>Anesu アネス Adrian Mupemhi</strong> for <strong>Benjamin Music Initiatives</strong><br>
        Copyright 2025
    </footer>

    <!-- Donate Button -->
    <button class="donate-button" onclick="window.open('https://paypal.me/benjimusic', '_blank')">
        Donate
    </button>

    <!-- Bootstrap JS (Optional) -->
    <script src="https://cdn.jsdelivr.net/npm/@popperjs/core@2.11.8/dist/umd/popper.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.min.js"></script>

    <!-- Language Toggle Script -->
    <script>
        let currentLanguage = 'en';

        function toggleDropdown() {
            document.getElementById("languageDropdown").classList.toggle("show");
        }

        function toggleQualitySettings() {
            const format = document.getElementById('format').value;
            const qualitySettings = document.getElementById('qualitySettings');

            // Show quality settings for compressed formats
            const compressedFormats = ['mp3', 'aac', 'ogg', 'm4a', 'wma', 'opus'];
            if (compressedFormats.includes(format)) {
                qualitySettings.style.display = 'block';
            } else {
                qualitySettings.style.display = 'none';
            }
        }

        function changeLanguage(lang) {
            currentLanguage = lang;

            // Update all elements with data attributes
            const elements = document.querySelectorAll('[data-en][data-sn]');
            elements.forEach(element => {
                const text = element.getAttribute('data-' + lang);
                if (text) {
                    element.innerHTML = text;
                }
            });

            // Update quality options
            const qualityOptions = document.querySelectorAll('#quality option');
            qualityOptions.forEach(option => {
                const text = option.getAttribute('data-' + lang);
                if (text) {
                    option.textContent = text;
                }
            });

            // Update page title
            const titles = {
                'en': 'Audio Convertor',
                'sn': 'Chishandura Maungira',
                'es': 'Conversor de Audio',
                'fr': 'Convertisseur Audio',
                'pt': 'Conversor de Áudio',
                'ja': 'オーディオコンバーター',
                'sw': 'Kibadilisha Sauti',
                'zu': 'Isikhongolose Sezwi'
            };
            document.title = titles[lang] || 'Audio Convertor';

            // Close dropdown
            document.getElementById("languageDropdown").classList.remove("show");
        }

        // Close dropdown when clicking outside
        window.onclick = function(event) {
            if (!event.target.matches('.language-toggle')) {
                var dropdowns = document.getElementsByClassName("dropdown-content");
                for (var i = 0; i < dropdowns.length; i++) {
                    var openDropdown = dropdowns[i];
                    if (openDropdown.classList.contains('show')) {
                        openDropdown.classList.remove('show');
                    }
                }
            }
        }

        // Initialize quality settings on page load
        document.addEventListener('DOMContentLoaded', function() {
            toggleQualitySettings();
        });
    </script>
</body>
</html>