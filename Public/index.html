<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><PERSON><PERSON><PERSON></title>
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Custom CSS -->
    <style>
        body {
            background: linear-gradient(135deg, #6a11cb, #2575fc);
            color: #fff;
            min-height: 100vh;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            margin: 0;
            padding: 20px;
        }
        .card {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 15px;
            padding: 2rem;
            width: 100%;
            max-width: 500px;
            margin: 0 auto;
        }
        .form-control, .form-select {
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            color: #fff;
        }
        .form-control:focus, .form-select:focus {
            background: rgba(255, 255, 255, 0.2);
            border-color: rgba(255, 255, 255, 0.4);
            color: #fff;
        }
        .btn-primary {
            background: #2575fc;
            border: none;
            transition: background 0.3s ease;
        }
        .btn-primary:hover {
            background: #6a11cb;
        }
        footer {
            margin-top: 2rem;
            text-align: center;
            font-size: 0.9rem;
            opacity: 0.8;
        }
        .donate-button {
            margin-top: 1.5rem;
            background: #28a745;
            border: none;
            padding: 0.75rem 1.5rem;
            border-radius: 50px;
            font-size: 1rem;
            color: #fff;
            cursor: pointer;
            transition: background 0.3s ease;
        }
        .donate-button:hover {
            background: #218838;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="card shadow-lg">
            <h1 class="text-center mb-4">🎵 Shandura Maungira</h1>
            <form action="/convert" method="post" enctype="multipart/form-data">
                <div class="mb-3">
                    <label for="audio" class="form-label">Upload Audio File:</label>
                    <input type="file" class="form-control" name="audio" id="audio" accept="audio/*" required>
                </div>
                <div class="mb-3">
                    <label for="format" class="form-label">Select Output Format:</label>
                    <select class="form-select" name="format" id="format">
                        <option value="mp3">MP3</option>
                        <option value="wav">WAV</option>
                        <option value="aac">AAC</option>
                        <option value="ogg">OGG</option>
                        <option value="flac">FLAC</option>
                        <option value="m4a">M4A</option>
                        <option value="aiff">AIFF</option>
                        <option value="wma">WMA</option>
                        <option value="opus">OPUS</option>
                    </select>
                </div>
                <button type="submit" class="btn btn-primary w-100">Convert</button>
            </form>
        </div>
    </div>

    <!-- Footer -->
    <footer>
        Created By <strong>Anesu Adrian Mupemhi</strong> & <strong>Tinotenda Moyo</strong>
    </footer>

    <!-- Donate Button -->
    <button class="donate-button" onclick="window.open('https://www.paypal.com/donate?hosted_button_id=YOUR_BUTTON_ID', '_blank')">
        Donate
    </button>

    <!-- Bootstrap JS (Optional) -->
    <script src="https://cdn.jsdelivr.net/npm/@popperjs/core@2.11.8/dist/umd/popper.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.min.js"></script>
</body>
</html>