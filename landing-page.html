<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Project Versioner Pro - Professional Project Versioning Made Simple</title>
    <meta name="description" content="Automate your project versioning with one-click updates, automatic changelogs, and professional exports. Save hours of manual work.">
    
    <!-- Open Graph / Social Media -->
    <meta property="og:title" content="Project Versioner Pro - Professional Project Versioning Made Simple">
    <meta property="og:description" content="Automate your project versioning with one-click updates, automatic changelogs, and professional exports.">
    <meta property="og:type" content="website">
    <meta property="og:url" content="https://projectversioner.com">
    
    <!-- Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Inter', sans-serif;
            line-height: 1.6;
            color: #333;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
        }
        
        /* Header */
        header {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            padding: 1rem 0;
            position: fixed;
            width: 100%;
            top: 0;
            z-index: 1000;
        }
        
        nav {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .logo {
            font-size: 1.5rem;
            font-weight: 700;
            color: white;
        }
        
        .nav-links {
            display: flex;
            list-style: none;
            gap: 2rem;
        }
        
        .nav-links a {
            color: white;
            text-decoration: none;
            font-weight: 500;
            transition: opacity 0.3s;
        }
        
        .nav-links a:hover {
            opacity: 0.8;
        }
        
        /* Hero Section */
        .hero {
            padding: 120px 0 80px;
            text-align: center;
            color: white;
        }
        
        .hero h1 {
            font-size: 3.5rem;
            font-weight: 700;
            margin-bottom: 1rem;
            line-height: 1.2;
        }
        
        .hero .subtitle {
            font-size: 1.3rem;
            margin-bottom: 2rem;
            opacity: 0.9;
            max-width: 600px;
            margin-left: auto;
            margin-right: auto;
        }
        
        .cta-buttons {
            display: flex;
            gap: 1rem;
            justify-content: center;
            flex-wrap: wrap;
            margin-bottom: 3rem;
        }
        
        .btn {
            padding: 15px 30px;
            border: none;
            border-radius: 8px;
            font-size: 1.1rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s;
            text-decoration: none;
            display: inline-block;
        }
        
        .btn-primary {
            background: #00d4aa;
            color: white;
            box-shadow: 0 4px 15px rgba(0, 212, 170, 0.3);
        }
        
        .btn-primary:hover {
            background: #00b894;
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(0, 212, 170, 0.4);
        }
        
        .btn-secondary {
            background: rgba(255, 255, 255, 0.2);
            color: white;
            border: 2px solid rgba(255, 255, 255, 0.3);
        }
        
        .btn-secondary:hover {
            background: rgba(255, 255, 255, 0.3);
            transform: translateY(-2px);
        }
        
        /* Demo Video */
        .demo-video {
            max-width: 800px;
            margin: 0 auto;
            border-radius: 15px;
            overflow: hidden;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
        }
        
        .demo-placeholder {
            background: #2d3748;
            color: white;
            padding: 100px 20px;
            text-align: center;
            font-size: 1.2rem;
        }
        
        /* Features Section */
        .features {
            padding: 80px 0;
            background: white;
        }
        
        .features h2 {
            text-align: center;
            font-size: 2.5rem;
            margin-bottom: 3rem;
            color: #2d3748;
        }
        
        .features-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 2rem;
        }
        
        .feature-card {
            background: #f8f9fa;
            padding: 2rem;
            border-radius: 12px;
            text-align: center;
            transition: transform 0.3s;
        }
        
        .feature-card:hover {
            transform: translateY(-5px);
        }
        
        .feature-icon {
            font-size: 3rem;
            color: #667eea;
            margin-bottom: 1rem;
        }
        
        .feature-card h3 {
            font-size: 1.3rem;
            margin-bottom: 1rem;
            color: #2d3748;
        }
        
        /* Pricing Section */
        .pricing {
            padding: 80px 0;
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            color: white;
        }
        
        .pricing h2 {
            text-align: center;
            font-size: 2.5rem;
            margin-bottom: 3rem;
        }
        
        .pricing-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 2rem;
            max-width: 900px;
            margin: 0 auto;
        }
        
        .pricing-card {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            padding: 2rem;
            border-radius: 15px;
            text-align: center;
            border: 2px solid rgba(255, 255, 255, 0.2);
        }
        
        .pricing-card.featured {
            border-color: #00d4aa;
            transform: scale(1.05);
        }
        
        .price {
            font-size: 3rem;
            font-weight: 700;
            margin: 1rem 0;
        }
        
        .price-period {
            font-size: 1rem;
            opacity: 0.8;
        }
        
        .features-list {
            list-style: none;
            margin: 2rem 0;
        }
        
        .features-list li {
            padding: 0.5rem 0;
        }
        
        .features-list i {
            color: #00d4aa;
            margin-right: 0.5rem;
        }
        
        /* Contact Section */
        .contact {
            padding: 80px 0;
            background: #2d3748;
            color: white;
            text-align: center;
        }
        
        .contact h2 {
            font-size: 2.5rem;
            margin-bottom: 2rem;
        }
        
        .contact-info {
            display: flex;
            justify-content: center;
            gap: 3rem;
            flex-wrap: wrap;
            margin-top: 2rem;
        }
        
        .contact-item {
            display: flex;
            align-items: center;
            gap: 1rem;
        }
        
        .contact-item i {
            font-size: 1.5rem;
            color: #00d4aa;
        }
        
        /* Footer */
        footer {
            background: #1a202c;
            color: white;
            text-align: center;
            padding: 2rem 0;
        }
        
        /* Responsive */
        @media (max-width: 768px) {
            .hero h1 {
                font-size: 2.5rem;
            }
            
            .hero .subtitle {
                font-size: 1.1rem;
            }
            
            .cta-buttons {
                flex-direction: column;
                align-items: center;
            }
            
            .nav-links {
                display: none;
            }
        }
        
        /* Animations */
        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
        
        .fade-in-up {
            animation: fadeInUp 0.6s ease-out;
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header>
        <nav class="container">
            <div class="logo">🚀 Project Versioner Pro</div>
            <ul class="nav-links">
                <li><a href="#features">Features</a></li>
                <li><a href="#pricing">Pricing</a></li>
                <li><a href="#contact">Contact</a></li>
            </ul>
        </nav>
    </header>

    <!-- Hero Section -->
    <section class="hero">
        <div class="container">
            <h1 class="fade-in-up">Professional Project Versioning Made Simple</h1>
            <p class="subtitle fade-in-up">Stop wasting hours on manual versioning. Automate everything with one-click updates, automatic changelogs, and professional exports.</p>
            
            <div class="cta-buttons fade-in-up">
                <a href="#contact" class="btn btn-primary">Get Started Free</a>
                <a href="#demo" class="btn btn-secondary">Watch Demo</a>
            </div>
            
            <div class="demo-video fade-in-up" id="demo">
                <div class="demo-placeholder">
                    🎬 Demo Video Coming Soon<br>
                    <small>See Project Versioner Pro in action</small>
                </div>
            </div>
        </div>
    </section>

    <!-- Features Section -->
    <section class="features" id="features">
        <div class="container">
            <h2>Why Developers Love Project Versioner Pro</h2>
            <div class="features-grid">
                <div class="feature-card">
                    <div class="feature-icon">⚡</div>
                    <h3>One-Click Versioning</h3>
                    <p>Update your project version with a single click. Choose major, minor, or patch updates with semantic versioning.</p>
                </div>

                <div class="feature-card">
                    <div class="feature-icon">📝</div>
                    <h3>Automatic Changelogs</h3>
                    <p>Generate beautiful changelogs automatically with Git integration. Never forget what changed between versions.</p>
                </div>

                <div class="feature-card">
                    <div class="feature-icon">📦</div>
                    <h3>Smart Exports</h3>
                    <p>Create professional ZIP archives with intelligent exclusions. No more node_modules or .git files in your exports.</p>
                </div>

                <div class="feature-card">
                    <div class="feature-icon">🌐</div>
                    <h3>Beautiful Interface</h3>
                    <p>Gorgeous web interface that's a joy to use. No more command line frustration or complex configurations.</p>
                </div>

                <div class="feature-card">
                    <div class="feature-icon">🔄</div>
                    <h3>Version History</h3>
                    <p>Track every change with detailed version history. See exactly what changed, when, and why.</p>
                </div>

                <div class="feature-card">
                    <div class="feature-icon">🛡️</div>
                    <h3>Zero Configuration</h3>
                    <p>Works out of the box with any Node.js project. No setup, no configuration files, just install and go.</p>
                </div>
            </div>
        </div>
    </section>

    <!-- Pricing Section -->
    <section class="pricing" id="pricing">
        <div class="container">
            <h2>Choose Your Plan</h2>
            <div class="pricing-grid">
                <div class="pricing-card">
                    <h3>Starter</h3>
                    <div class="price">Free</div>
                    <p class="price-period">Forever</p>
                    <ul class="features-list">
                        <li><i class="fas fa-check"></i> 3 Projects</li>
                        <li><i class="fas fa-check"></i> Basic Versioning</li>
                        <li><i class="fas fa-check"></i> ZIP Exports</li>
                        <li><i class="fas fa-check"></i> Community Support</li>
                    </ul>
                    <a href="#contact" class="btn btn-secondary">Get Started</a>
                </div>

                <div class="pricing-card featured">
                    <h3>Professional</h3>
                    <div class="price">$19<span class="price-period">/month</span></div>
                    <ul class="features-list">
                        <li><i class="fas fa-check"></i> Unlimited Projects</li>
                        <li><i class="fas fa-check"></i> Advanced Features</li>
                        <li><i class="fas fa-check"></i> Custom Templates</li>
                        <li><i class="fas fa-check"></i> Priority Support</li>
                        <li><i class="fas fa-check"></i> API Access</li>
                    </ul>
                    <a href="#contact" class="btn btn-primary">Start Free Trial</a>
                </div>

                <div class="pricing-card">
                    <h3>Enterprise</h3>
                    <div class="price">Custom</div>
                    <p class="price-period">Contact Us</p>
                    <ul class="features-list">
                        <li><i class="fas fa-check"></i> White Labeling</li>
                        <li><i class="fas fa-check"></i> Custom Integrations</li>
                        <li><i class="fas fa-check"></i> On-Premise Deployment</li>
                        <li><i class="fas fa-check"></i> Dedicated Support</li>
                        <li><i class="fas fa-check"></i> Training & Consulting</li>
                    </ul>
                    <a href="#contact" class="btn btn-secondary">Contact Sales</a>
                </div>
            </div>
        </div>
    </section>

    <!-- Contact Section -->
    <section class="contact" id="contact">
        <div class="container">
            <h2>Ready to Transform Your Workflow?</h2>
            <p>Join thousands of developers who have already streamlined their project versioning.</p>

            <div class="contact-info">
                <div class="contact-item">
                    <i class="fas fa-envelope"></i>
                    <div>
                        <strong>Email</strong><br>
                        <EMAIL>
                    </div>
                </div>

                <div class="contact-item">
                    <i class="fab fa-paypal"></i>
                    <div>
                        <strong>Support Us</strong><br>
                        <a href="https://paypal.me/benjimusic" target="_blank" style="color: #00d4aa;">paypal.me/benjimusic</a>
                    </div>
                </div>

                <div class="contact-item">
                    <i class="fas fa-building"></i>
                    <div>
                        <strong>Company</strong><br>
                        Benjamin Music Initiatives
                    </div>
                </div>
            </div>

            <div style="margin-top: 3rem;">
                <a href="mailto:<EMAIL>?subject=Project Versioner Pro Inquiry" class="btn btn-primary">Get Started Today</a>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer>
        <div class="container">
            <p>&copy; 2025 Project Versioner Pro - Created by Adrian Anesu Mupemhi for Benjamin Music Initiatives</p>
            <p>All rights reserved. Professional project versioning made simple.</p>
        </div>
    </footer>

    <script>
        // Smooth scrolling for navigation links
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
            });
        });

        // Add animation on scroll
        const observerOptions = {
            threshold: 0.1,
            rootMargin: '0px 0px -50px 0px'
        };

        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.classList.add('fade-in-up');
                }
            });
        }, observerOptions);

        // Observe all feature cards and pricing cards
        document.querySelectorAll('.feature-card, .pricing-card').forEach(card => {
            observer.observe(card);
        });
    </script>
</body>
</html>
