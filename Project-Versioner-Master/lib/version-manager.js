/**
 * Version Manager
 * 
 * Copyright © 2025 - Curated by <PERSON> For Benjamin Music Initiatives
 * All rights reserved.
 * 
 * This module handles version updates and change tracking.
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

/**
 * Updates the project version and records changes
 * @param {string} projectDir - The root directory of the project
 * @param {string} versionType - The type of version update: 'major', 'minor', or 'patch'
 * @param {string} changeDescription - Description of changes made in this version
 * @returns {Object} - Object containing the old and new version numbers
 */
function updateVersion(projectDir, versionType = 'patch', changeDescription = '') {
  // Read the package.json file
  const packageJsonPath = path.join(projectDir, 'package.json');
  const packageJsonContent = fs.readFileSync(packageJsonPath, 'utf8');
  const packageJson = JSON.parse(packageJsonContent);
  
  // Store the old version
  const oldVersion = packageJson.version;
  
  // Parse the version components
  const [major, minor, patch] = oldVersion.split('.').map(Number);
  
  // Calculate the new version based on the version type
  let newVersion;
  switch (versionType.toLowerCase()) {
    case 'major':
      newVersion = `${major + 1}.0.0`;
      break;
    case 'minor':
      newVersion = `${major}.${minor + 1}.0`;
      break;
    case 'patch':
    default:
      newVersion = `${major}.${minor}.${patch + 1}`;
      break;
  }
  
  // Update the version in package.json
  packageJson.version = newVersion;
  fs.writeFileSync(packageJsonPath, JSON.stringify(packageJson, null, 2) + '\n');
  
  // Record the changes in the changelog
  recordChanges(projectDir, oldVersion, newVersion, changeDescription);
  
  // Return the version information
  return {
    oldVersion,
    newVersion
  };
}

/**
 * Records changes in the changelog file
 * @param {string} projectDir - The root directory of the project
 * @param {string} oldVersion - The previous version
 * @param {string} newVersion - The new version
 * @param {string} changeDescription - Description of changes made
 */
function recordChanges(projectDir, oldVersion, newVersion, changeDescription) {
  const changelogPath = path.join(projectDir, 'CHANGELOG.md');
  const timestamp = new Date().toISOString().split('T')[0]; // YYYY-MM-DD format
  
  // Create changelog entry
  let changelogEntry = `\n## [${newVersion}] - ${timestamp}\n\n`;
  
  // Add change description if provided
  if (changeDescription && changeDescription.trim()) {
    changelogEntry += `${changeDescription.trim()}\n\n`;
  }
  
  // Try to get git changes since last version
  try {
    // Check if git is available and if this is a git repository
    const isGitRepo = fs.existsSync(path.join(projectDir, '.git'));
    
    if (isGitRepo) {
      // Get git commits since last version
      const gitLog = execSync(
        `git log --pretty=format:"- %s" $(git describe --tags --abbrev=0 2>/dev/null || git rev-list --max-parents=0 HEAD)..HEAD`,
        { cwd: projectDir, stdio: ['pipe', 'pipe', 'ignore'] }
      ).toString().trim();
      
      if (gitLog) {
        changelogEntry += `### Changes\n${gitLog}\n\n`;
      }
    }
  } catch (error) {
    // If there's an error with git commands, just continue without git info
    console.log('Could not retrieve git changes:', error.message);
  }
  
  // Create or update the changelog file
  let changelog = '';
  if (fs.existsSync(changelogPath)) {
    changelog = fs.readFileSync(changelogPath, 'utf8');
  } else {
    changelog = `# Changelog\n\nAll notable changes to this project will be documented in this file.\n`;
  }
  
  // Insert the new entry after the header
  const headerEndIndex = changelog.indexOf('\n\n') + 2;
  changelog = changelog.substring(0, headerEndIndex) + changelogEntry + changelog.substring(headerEndIndex);
  
  // Write the updated changelog
  fs.writeFileSync(changelogPath, changelog);
  
  console.log(`Updated CHANGELOG.md with version ${newVersion} changes`);
}

/**
 * Gets the list of all versions from the changelog
 * @param {string} projectDir - The root directory of the project
 * @returns {Array} - Array of version objects with version number and date
 */
function getVersionHistory(projectDir) {
  const changelogPath = path.join(projectDir, 'CHANGELOG.md');
  
  if (!fs.existsSync(changelogPath)) {
    return [];
  }
  
  const changelog = fs.readFileSync(changelogPath, 'utf8');
  const versionRegex = /## \[([0-9.]+)\] - (\d{4}-\d{2}-\d{2})/g;
  
  const versions = [];
  let match;
  
  while ((match = versionRegex.exec(changelog)) !== null) {
    versions.push({
      version: match[1],
      date: match[2]
    });
  }
  
  return versions;
}

/**
 * Gets the changes for a specific version
 * @param {string} projectDir - The root directory of the project
 * @param {string} version - The version to get changes for
 * @returns {string} - The changes for the specified version
 */
function getVersionChanges(projectDir, version) {
  const changelogPath = path.join(projectDir, 'CHANGELOG.md');
  
  if (!fs.existsSync(changelogPath)) {
    return 'No changelog found';
  }
  
  const changelog = fs.readFileSync(changelogPath, 'utf8');
  const versionHeaderRegex = new RegExp(`## \\[${version}\\] - \\d{4}-\\d{2}-\\d{2}`);
  
  const versionHeaderMatch = versionHeaderRegex.exec(changelog);
  if (!versionHeaderMatch) {
    return `No changes found for version ${version}`;
  }
  
  const versionHeaderIndex = versionHeaderMatch.index;
  const nextVersionHeaderMatch = /## \[[0-9.]+\] - \d{4}-\d{2}-\d{2}/g.exec(changelog.substring(versionHeaderIndex + 1));
  
  const endIndex = nextVersionHeaderMatch 
    ? versionHeaderIndex + 1 + nextVersionHeaderMatch.index 
    : changelog.length;
  
  return changelog.substring(versionHeaderIndex, endIndex).trim();
}

module.exports = {
  updateVersion,
  recordChanges,
  getVersionHistory,
  getVersionChanges
};
