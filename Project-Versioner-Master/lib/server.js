/**
 * Project Versioner Server
 * 
 * Copyright © 2025 - Curated by <PERSON> For Benjamin Music Initiatives
 * All rights reserved.
 */

const express = require('express');
const path = require('path');
const fs = require('fs');
const { createProjectZip } = require('./create-zip');
const { 
  updateVersion, 
  getVersionHistory, 
  getVersionChanges 
} = require('./version-manager');

/**
 * Starts the versioning server
 * @param {number} port - The port to run the server on
 * @param {string} projectDir - The root directory of the project
 * @returns {Object} - The Express app and server instance
 */
function startServer(port = 3001, projectDir = process.cwd()) {
  const app = express();
  
  // Parse JSON requests
  app.use(express.json());
  
  // Serve static files from the public directory of our package
  app.use(express.static(path.join(__dirname, '../public')));
  
  // API endpoint to create a ZIP file
  app.post('/api/create-zip', async (req, res) => {
    const { version } = req.body;
    
    if (!version) {
      return res.status(400).json({ message: 'Version is required' });
    }
    
    try {
      const zipFileName = await createProjectZip(version, projectDir);
      res.json({ message: `Successfully created ${zipFileName}` });
    } catch (error) {
      console.error(`Error creating ZIP: ${error.message}`);
      res.status(500).json({ message: `Error creating ZIP: ${error.message}` });
    }
  });
  
  // API endpoint to get project info
  app.get('/api/project-info', (req, res) => {
    try {
      const packageJsonPath = path.join(projectDir, 'package.json');
      const packageJson = JSON.parse(fs.readFileSync(packageJsonPath, 'utf8'));
      
      res.json({
        name: packageJson.name,
        version: packageJson.version,
        description: packageJson.description || ''
      });
    } catch (error) {
      console.error(`Error getting project info: ${error.message}`);
      res.status(500).json({ message: `Error getting project info: ${error.message}` });
    }
  });
  
  // API endpoint to update version
  app.post('/api/update-version', (req, res) => {
    const { versionType, changeDescription } = req.body;
    
    if (!versionType) {
      return res.status(400).json({ message: 'Version type is required (major, minor, or patch)' });
    }
    
    try {
      const result = updateVersion(projectDir, versionType, changeDescription);
      res.json({ 
        message: `Successfully updated version from ${result.oldVersion} to ${result.newVersion}`,
        oldVersion: result.oldVersion,
        newVersion: result.newVersion
      });
    } catch (error) {
      console.error(`Error updating version: ${error.message}`);
      res.status(500).json({ message: `Error updating version: ${error.message}` });
    }
  });
  
  // API endpoint to get version history
  app.get('/api/version-history', (req, res) => {
    try {
      const versions = getVersionHistory(projectDir);
      res.json({ versions });
    } catch (error) {
      console.error(`Error getting version history: ${error.message}`);
      res.status(500).json({ message: `Error getting version history: ${error.message}` });
    }
  });
  
  // API endpoint to get changes for a specific version
  app.get('/api/version-changes/:version', (req, res) => {
    const { version } = req.params;
    
    if (!version) {
      return res.status(400).json({ message: 'Version is required' });
    }
    
    try {
      const changes = getVersionChanges(projectDir, version);
      res.json({ version, changes });
    } catch (error) {
      console.error(`Error getting version changes: ${error.message}`);
      res.status(500).json({ message: `Error getting version changes: ${error.message}` });
    }
  });
  
  // Start the server
  const server = app.listen(port, () => {
    console.log(`Project Versioner running at http://localhost:${port}`);
    console.log(`Open http://localhost:${port}/export-version-pro.html for the polished interface`);
    console.log(`Open http://localhost:${port}/export-version.html for the classic interface`);
    console.log(`Copyright © 2025 - Curated by Adrian Anesu Mupemhi For Benjamin Music Initiatives`);
  });
  
  return { app, server };
}

module.exports = { startServer };
