/**
 * Project Versioner ZIP Creator
 * 
 * Copyright © 2025 - Curated by <PERSON> For Benjamin Music Initiatives
 * All rights reserved.
 */

const fs = require('fs');
const path = require('path');
const archiver = require('archiver');

/**
 * Creates a ZIP file of the project
 * @param {string} version - The version number to use in the filename
 * @param {string} projectDir - The root directory of the project
 * @returns {Promise<string>} - The path to the created ZIP file
 */
function createProjectZip(version, projectDir) {
  return new Promise((resolve, reject) => {
    try {
      // Get package.json for project name
      const packageJsonPath = path.join(projectDir, 'package.json');
      const packageJson = require(packageJsonPath);
      const projectName = packageJson.name;
      
      // Create a file to stream archive data to
      const zipFileName = `${projectName}-${version}.zip`;
      const zipFilePath = path.join(projectDir, zipFileName);
      const output = fs.createWriteStream(zipFilePath);
      const archive = archiver('zip', {
        zlib: { level: 9 } // Sets the compression level
      });

      // Listen for all archive data to be written
      output.on('close', function() {
        console.log(`Archive created: ${zipFileName}, total bytes: ${archive.pointer()}`);
        resolve(zipFileName);
      });

      // Handle warnings and errors
      archive.on('warning', function(err) {
        if (err.code === 'ENOENT') {
          console.warn(err);
        } else {
          reject(err);
        }
      });

      archive.on('error', function(err) {
        reject(err);
      });

      // Pipe archive data to the file
      archive.pipe(output);

      // Add files and directories to the archive
      // Exclude node_modules, .git, and the zip file itself
      const excludeDirs = ['node_modules', '.git'];
      
      // Function to recursively add files to the archive
      function addDirectoryToArchive(dirPath, archivePath) {
        const files = fs.readdirSync(dirPath);
        
        for (const file of files) {
          const filePath = path.join(dirPath, file);
          const relativePath = path.relative(projectDir, filePath);
          
          // Skip excluded directories
          if (excludeDirs.some(dir => relativePath.startsWith(dir))) {
            continue;
          }
          
          // Skip zip files
          if (filePath.endsWith('.zip')) {
            continue;
          }
          
          const stats = fs.statSync(filePath);
          
          if (stats.isDirectory()) {
            // Recursively add subdirectories
            addDirectoryToArchive(filePath, path.join(archivePath, file));
          } else {
            // Add file to archive with project name from package.json
            archive.file(filePath, { name: path.join(`${projectName}-${version}`, relativePath) });
          }
        }
      }
      
      // Start adding files from the root directory
      addDirectoryToArchive(projectDir, '');
      
      // Finalize the archive
      archive.finalize();
    } catch (error) {
      reject(error);
    }
  });
}

module.exports = { createProjectZip };
