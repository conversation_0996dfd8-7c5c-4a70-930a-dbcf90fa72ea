<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Project Versioner Pro - Professional Project Versioning</title>
    <link href="https://fonts.googleapis.com/css2?family=JetBrains+Mono:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    
    <style>
        :root {
            /* Modern Dark Theme */
            --bg-primary: #0a0e1a;
            --bg-secondary: #1a1f2e;
            --bg-tertiary: #252a3a;
            --accent-primary: #00d4aa;
            --accent-secondary: #64ffda;
            --accent-tertiary: #18ffff;
            --text-primary: #ffffff;
            --text-secondary: #b0bec5;
            --text-muted: #78909c;
            --border-color: #37474f;
            --success-color: #4caf50;
            --warning-color: #ff9800;
            --error-color: #f44336;
            --shadow-glow: 0 0 20px rgba(0, 212, 170, 0.3);
            --shadow-soft: 0 4px 20px rgba(0, 0, 0, 0.3);
            --border-radius: 12px;
            --transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'JetBrains Mono', monospace;
            background: linear-gradient(135deg, var(--bg-primary) 0%, #0f1419 50%, var(--bg-primary) 100%);
            color: var(--text-primary);
            min-height: 100vh;
            overflow-x: hidden;
            position: relative;
        }

        /* Animated Background */
        .bg-animation {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            z-index: -1;
            opacity: 0.1;
        }

        .floating-shapes {
            position: absolute;
            width: 100%;
            height: 100%;
        }

        .shape {
            position: absolute;
            background: linear-gradient(45deg, var(--accent-primary), var(--accent-secondary));
            border-radius: 50%;
            animation: float 6s ease-in-out infinite;
        }

        .shape:nth-child(1) { width: 80px; height: 80px; top: 20%; left: 10%; animation-delay: 0s; }
        .shape:nth-child(2) { width: 60px; height: 60px; top: 60%; left: 80%; animation-delay: 2s; }
        .shape:nth-child(3) { width: 40px; height: 40px; top: 80%; left: 20%; animation-delay: 4s; }

        @keyframes float {
            0%, 100% { transform: translateY(0px) rotate(0deg); }
            50% { transform: translateY(-20px) rotate(180deg); }
        }

        /* Container */
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 2rem;
        }

        /* Header */
        .header {
            text-align: center;
            margin-bottom: 3rem;
            position: relative;
        }

        .logo {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 1rem;
            margin-bottom: 1rem;
        }

        .logo-icon {
            width: 60px;
            height: 60px;
            background: linear-gradient(135deg, var(--accent-primary), var(--accent-secondary));
            border-radius: var(--border-radius);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.8rem;
            color: white;
            box-shadow: var(--shadow-glow);
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.05); }
        }

        .title {
            font-size: 2.5rem;
            font-weight: 700;
            background: linear-gradient(135deg, var(--accent-primary), var(--accent-secondary));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            margin-bottom: 0.5rem;
        }

        .subtitle {
            color: var(--text-secondary);
            font-size: 1.1rem;
            font-weight: 300;
        }

        /* Project Info Card */
        .project-card {
            background: var(--bg-secondary);
            border: 1px solid var(--border-color);
            border-radius: var(--border-radius);
            padding: 2rem;
            margin-bottom: 2rem;
            box-shadow: var(--shadow-soft);
            position: relative;
            overflow: hidden;
        }

        .project-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 3px;
            background: linear-gradient(90deg, var(--accent-primary), var(--accent-secondary), var(--accent-tertiary));
        }

        .project-info {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 1.5rem;
        }

        .info-item {
            display: flex;
            flex-direction: column;
            gap: 0.5rem;
        }

        .info-label {
            color: var(--text-muted);
            font-size: 0.9rem;
            text-transform: uppercase;
            letter-spacing: 1px;
            font-weight: 500;
        }

        .info-value {
            color: var(--text-primary);
            font-size: 1.1rem;
            font-weight: 600;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .status-badge {
            padding: 0.25rem 0.75rem;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: 500;
            background: linear-gradient(135deg, var(--success-color), #66bb6a);
            color: white;
        }

        /* Navigation Tabs */
        .nav-tabs {
            display: flex;
            background: var(--bg-tertiary);
            border-radius: var(--border-radius);
            padding: 0.5rem;
            margin-bottom: 2rem;
            box-shadow: var(--shadow-soft);
        }

        .nav-tab {
            flex: 1;
            padding: 1rem 1.5rem;
            text-align: center;
            cursor: pointer;
            border-radius: calc(var(--border-radius) - 4px);
            transition: var(--transition);
            font-weight: 500;
            color: var(--text-secondary);
            position: relative;
        }

        .nav-tab.active {
            background: linear-gradient(135deg, var(--accent-primary), var(--accent-secondary));
            color: white;
            box-shadow: var(--shadow-glow);
        }

        .nav-tab:hover:not(.active) {
            background: rgba(255, 255, 255, 0.05);
            color: var(--text-primary);
        }

        /* Tab Content */
        .tab-content {
            display: none;
            animation: fadeInUp 0.5s ease-out;
        }

        .tab-content.active {
            display: block;
        }

        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        /* Action Buttons */
        .action-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1.5rem;
            margin-bottom: 2rem;
        }

        .action-card {
            background: var(--bg-secondary);
            border: 1px solid var(--border-color);
            border-radius: var(--border-radius);
            padding: 2rem;
            text-align: center;
            cursor: pointer;
            transition: var(--transition);
            position: relative;
            overflow: hidden;
        }

        .action-card:hover {
            transform: translateY(-5px);
            box-shadow: var(--shadow-glow);
            border-color: var(--accent-primary);
        }

        .action-icon {
            font-size: 2.5rem;
            margin-bottom: 1rem;
            background: linear-gradient(135deg, var(--accent-primary), var(--accent-secondary));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .action-title {
            font-size: 1.2rem;
            font-weight: 600;
            margin-bottom: 0.5rem;
            color: var(--text-primary);
        }

        .action-description {
            color: var(--text-secondary);
            font-size: 0.9rem;
            line-height: 1.5;
        }

        /* Modern Button */
        .btn {
            background: linear-gradient(135deg, var(--accent-primary), var(--accent-secondary));
            color: white;
            border: none;
            padding: 1rem 2rem;
            border-radius: var(--border-radius);
            font-family: inherit;
            font-weight: 600;
            cursor: pointer;
            transition: var(--transition);
            box-shadow: var(--shadow-soft);
            position: relative;
            overflow: hidden;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: var(--shadow-glow);
        }

        .btn:active {
            transform: translateY(0);
        }

        .btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }

        .btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
            transition: left 0.5s;
        }

        .btn:hover::before {
            left: 100%;
        }

        /* Messages */
        .message {
            padding: 1rem 1.5rem;
            border-radius: var(--border-radius);
            margin: 1rem 0;
            display: none;
            animation: slideIn 0.3s ease-out;
        }

        .message.success {
            background: rgba(76, 175, 80, 0.1);
            border: 1px solid var(--success-color);
            color: var(--success-color);
        }

        .message.error {
            background: rgba(244, 67, 54, 0.1);
            border: 1px solid var(--error-color);
            color: var(--error-color);
        }

        @keyframes slideIn {
            from { transform: translateX(-100%); opacity: 0; }
            to { transform: translateX(0); opacity: 1; }
        }

        /* Responsive */
        @media (max-width: 768px) {
            .container { padding: 1rem; }
            .title { font-size: 2rem; }
            .project-info { grid-template-columns: 1fr; }
            .action-grid { grid-template-columns: 1fr; }
            .nav-tabs { flex-direction: column; }
        }
    </style>
</head>
<body>
    <!-- Animated Background -->
    <div class="bg-animation">
        <div class="floating-shapes">
            <div class="shape"></div>
            <div class="shape"></div>
            <div class="shape"></div>
        </div>
    </div>

    <div class="container">
        <!-- Header -->
        <header class="header">
            <div class="logo">
                <div class="logo-icon">
                    <i class="fas fa-rocket"></i>
                </div>
                <div>
                    <h1 class="title">Project Versioner Pro</h1>
                    <p class="subtitle">Professional Project Versioning Made Simple</p>
                </div>
            </div>
        </header>

        <!-- Project Info Card -->
        <div class="project-card">
            <div class="project-info">
                <div class="info-item">
                    <span class="info-label">Project Name</span>
                    <span class="info-value" id="project-name">
                        <i class="fas fa-folder"></i>
                        Loading...
                    </span>
                </div>
                <div class="info-item">
                    <span class="info-label">Current Version</span>
                    <span class="info-value" id="project-version">
                        <i class="fas fa-tag"></i>
                        Loading...
                    </span>
                </div>
                <div class="info-item">
                    <span class="info-label">Status</span>
                    <span class="info-value">
                        <span class="status-badge">
                            <i class="fas fa-check"></i>
                            Ready
                        </span>
                    </span>
                </div>
            </div>
            <div class="info-item" style="margin-top: 1.5rem;">
                <span class="info-label">Description</span>
                <span class="info-value" id="project-description">Loading...</span>
            </div>
        </div>

        <!-- Navigation Tabs -->
        <div class="nav-tabs">
            <div class="nav-tab active" data-tab="export">
                <i class="fas fa-download"></i>
                Export Project
            </div>
            <div class="nav-tab" data-tab="versions">
                <i class="fas fa-history"></i>
                Version History
            </div>
        </div>

        <!-- Export Tab -->
        <div id="export-tab" class="tab-content active">
            <div class="action-grid">
                <div class="action-card" onclick="exportVersion()">
                    <div class="action-icon">
                        <i class="fas fa-file-archive"></i>
                    </div>
                    <h3 class="action-title">Export as ZIP</h3>
                    <p class="action-description">Create a professional ZIP archive of your project with smart exclusions</p>
                </div>

                <div class="action-card" onclick="showUpdateVersionModal()">
                    <div class="action-icon">
                        <i class="fas fa-arrow-up"></i>
                    </div>
                    <h3 class="action-title">Update Version</h3>
                    <p class="action-description">Increment version number and generate automatic changelog</p>
                </div>
            </div>

            <div id="success-message" class="message success"></div>
            <div id="error-message" class="message error"></div>
        </div>

        <!-- Version History Tab -->
        <div id="versions-tab" class="tab-content">
            <div class="project-card">
                <h2 style="margin-bottom: 1.5rem; color: var(--text-primary);">
                    <i class="fas fa-history"></i>
                    Version History
                </h2>
                <div id="version-history">
                    <div id="version-list" class="version-list">
                        <div class="loading">Loading version history...</div>
                    </div>
                </div>

                <div id="version-details" style="display: none; margin-top: 2rem;">
                    <h3 style="color: var(--accent-primary); margin-bottom: 1rem;">
                        Changes in version <span id="selected-version"></span>
                    </h3>
                    <div id="version-changes" class="version-changes">
                        Loading changes...
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Update Version Modal -->
    <div id="update-version-modal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h2><i class="fas fa-arrow-up"></i> Update Version</h2>
                <span class="close" onclick="closeUpdateVersionModal()">
                    <i class="fas fa-times"></i>
                </span>
            </div>

            <div class="modal-body">
                <div class="version-info">
                    <p>Current version: <span id="current-version-display" class="current-version">Loading...</span></p>
                </div>

                <div class="version-types">
                    <h3>Select version type:</h3>
                    <div class="version-option">
                        <label>
                            <input type="radio" name="version-type" value="major">
                            <span class="radio-custom"></span>
                            <div class="option-content">
                                <strong>Major (x.0.0)</strong>
                                <small>Incompatible API changes</small>
                            </div>
                        </label>
                    </div>

                    <div class="version-option">
                        <label>
                            <input type="radio" name="version-type" value="minor" checked>
                            <span class="radio-custom"></span>
                            <div class="option-content">
                                <strong>Minor (0.x.0)</strong>
                                <small>Add functionality in backward-compatible manner</small>
                            </div>
                        </label>
                    </div>

                    <div class="version-option">
                        <label>
                            <input type="radio" name="version-type" value="patch">
                            <span class="radio-custom"></span>
                            <div class="option-content">
                                <strong>Patch (0.0.x)</strong>
                                <small>Backward-compatible bug fixes</small>
                            </div>
                        </label>
                    </div>
                </div>

                <div class="description-section">
                    <h3>Describe the changes:</h3>
                    <textarea id="change-description" placeholder="Describe what changed in this version..."></textarea>
                </div>

                <button class="btn" onclick="updateVersion()">
                    <i class="fas fa-save"></i>
                    Update Version
                </button>
            </div>
        </div>
    </div>

    <style>
        /* Modal Styles */
        .modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.8);
            backdrop-filter: blur(10px);
        }

        .modal-content {
            background: var(--bg-secondary);
            margin: 5% auto;
            border-radius: var(--border-radius);
            width: 90%;
            max-width: 600px;
            box-shadow: var(--shadow-glow);
            animation: modalSlideIn 0.3s ease-out;
            border: 1px solid var(--border-color);
        }

        @keyframes modalSlideIn {
            from { transform: translateY(-50px); opacity: 0; }
            to { transform: translateY(0); opacity: 1; }
        }

        .modal-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 2rem 2rem 1rem;
            border-bottom: 1px solid var(--border-color);
        }

        .modal-header h2 {
            color: var(--text-primary);
            font-size: 1.5rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .close {
            background: none;
            border: none;
            color: var(--text-muted);
            font-size: 1.5rem;
            cursor: pointer;
            padding: 0.5rem;
            border-radius: 50%;
            transition: var(--transition);
        }

        .close:hover {
            background: rgba(255, 255, 255, 0.1);
            color: var(--text-primary);
        }

        .modal-body {
            padding: 2rem;
        }

        .version-info {
            margin-bottom: 2rem;
            padding: 1rem;
            background: var(--bg-tertiary);
            border-radius: var(--border-radius);
            border: 1px solid var(--border-color);
        }

        .current-version {
            color: var(--accent-primary);
            font-weight: 600;
        }

        .version-types {
            margin-bottom: 2rem;
        }

        .version-types h3 {
            color: var(--text-primary);
            margin-bottom: 1rem;
        }

        .version-option {
            margin-bottom: 1rem;
        }

        .version-option label {
            display: flex;
            align-items: center;
            gap: 1rem;
            padding: 1rem;
            border: 1px solid var(--border-color);
            border-radius: var(--border-radius);
            cursor: pointer;
            transition: var(--transition);
        }

        .version-option label:hover {
            border-color: var(--accent-primary);
            background: rgba(0, 212, 170, 0.05);
        }

        .version-option input[type="radio"] {
            display: none;
        }

        .radio-custom {
            width: 20px;
            height: 20px;
            border: 2px solid var(--border-color);
            border-radius: 50%;
            position: relative;
            transition: var(--transition);
        }

        .version-option input[type="radio"]:checked + .radio-custom {
            border-color: var(--accent-primary);
        }

        .version-option input[type="radio"]:checked + .radio-custom::after {
            content: '';
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            width: 10px;
            height: 10px;
            background: var(--accent-primary);
            border-radius: 50%;
        }

        .option-content strong {
            color: var(--text-primary);
            display: block;
        }

        .option-content small {
            color: var(--text-secondary);
        }

        .description-section h3 {
            color: var(--text-primary);
            margin-bottom: 1rem;
        }

        textarea {
            width: 100%;
            min-height: 120px;
            padding: 1rem;
            border: 1px solid var(--border-color);
            border-radius: var(--border-radius);
            background: var(--bg-tertiary);
            color: var(--text-primary);
            font-family: inherit;
            resize: vertical;
            transition: var(--transition);
        }

        textarea:focus {
            outline: none;
            border-color: var(--accent-primary);
            box-shadow: 0 0 0 3px rgba(0, 212, 170, 0.1);
        }

        /* Version List Styles */
        .version-list {
            max-height: 400px;
            overflow-y: auto;
        }

        .version-item {
            padding: 1rem;
            border: 1px solid var(--border-color);
            border-radius: var(--border-radius);
            margin-bottom: 1rem;
            cursor: pointer;
            transition: var(--transition);
            background: var(--bg-tertiary);
        }

        .version-item:hover {
            border-color: var(--accent-primary);
            transform: translateX(5px);
            box-shadow: var(--shadow-soft);
        }

        .version-changes {
            background: var(--bg-tertiary);
            border: 1px solid var(--border-color);
            border-radius: var(--border-radius);
            padding: 1.5rem;
            white-space: pre-wrap;
            font-family: 'JetBrains Mono', monospace;
            color: var(--text-secondary);
            line-height: 1.6;
        }

        .loading {
            text-align: center;
            color: var(--text-muted);
            padding: 2rem;
        }

        /* Footer */
        .footer {
            text-align: center;
            padding: 2rem;
            color: var(--text-muted);
            border-top: 1px solid var(--border-color);
            margin-top: 3rem;
        }
    </style>

    <script>
        // Initialize the application
        document.addEventListener('DOMContentLoaded', function() {
            fetchProjectInfo();
            setupTabs();
            loadVersionHistory();
        });

        // Tab functionality
        function setupTabs() {
            const tabs = document.querySelectorAll('.nav-tab');
            tabs.forEach(tab => {
                tab.addEventListener('click', () => {
                    // Remove active class from all tabs and content
                    document.querySelectorAll('.nav-tab').forEach(t => t.classList.remove('active'));
                    document.querySelectorAll('.tab-content').forEach(c => c.classList.remove('active'));

                    // Add active class to clicked tab and corresponding content
                    tab.classList.add('active');
                    const tabName = tab.getAttribute('data-tab');
                    document.getElementById(`${tabName}-tab`).classList.add('active');

                    // Load version history if versions tab is selected
                    if (tabName === 'versions') {
                        loadVersionHistory();
                    }
                });
            });
        }

        // Fetch project information
        function fetchProjectInfo() {
            fetch('/api/project-info')
                .then(response => {
                    if (!response.ok) throw new Error('Failed to fetch project info');
                    return response.json();
                })
                .then(data => {
                    document.getElementById('project-name').innerHTML = `<i class="fas fa-folder"></i> ${data.name}`;
                    document.getElementById('project-version').innerHTML = `<i class="fas fa-tag"></i> ${data.version}`;
                    document.getElementById('current-version-display').textContent = data.version;
                    document.getElementById('project-description').textContent = data.description || 'No description available';
                })
                .catch(error => {
                    showError('Error loading project info: ' + error.message);
                });
        }

        // Load version history
        function loadVersionHistory() {
            fetch('/api/version-history')
                .then(response => {
                    if (!response.ok) throw new Error('Failed to fetch version history');
                    return response.json();
                })
                .then(data => {
                    const versionList = document.getElementById('version-list');

                    if (data.versions && data.versions.length > 0) {
                        versionList.innerHTML = '';
                        data.versions.forEach(version => {
                            const div = document.createElement('div');
                            div.className = 'version-item';
                            div.innerHTML = `
                                <div style="display: flex; justify-content: space-between; align-items: center;">
                                    <span style="color: var(--accent-primary); font-weight: 600;">
                                        <i class="fas fa-tag"></i> Version ${version.version}
                                    </span>
                                    <span style="color: var(--text-muted); font-size: 0.9rem;">
                                        <i class="fas fa-calendar"></i> ${version.date}
                                    </span>
                                </div>
                            `;
                            div.onclick = () => showVersionDetails(version.version);
                            versionList.appendChild(div);
                        });
                    } else {
                        versionList.innerHTML = '<div class="loading">No version history available</div>';
                    }
                })
                .catch(error => {
                    document.getElementById('version-list').innerHTML = `<div class="loading">Error loading version history: ${error.message}</div>`;
                });
        }

        // Show version details
        function showVersionDetails(version) {
            document.getElementById('selected-version').textContent = version;
            document.getElementById('version-details').style.display = 'block';
            document.getElementById('version-changes').textContent = 'Loading changes...';

            fetch(`/api/version-changes/${version}`)
                .then(response => {
                    if (!response.ok) throw new Error('Failed to fetch version changes');
                    return response.json();
                })
                .then(data => {
                    document.getElementById('version-changes').textContent = data.changes;
                })
                .catch(error => {
                    document.getElementById('version-changes').textContent = `Error loading changes: ${error.message}`;
                });
        }

        // Export version
        function exportVersion() {
            const version = document.getElementById('project-version').textContent.replace('🏷️ ', '').trim();

            showMessage('Creating ZIP archive...', 'info');

            fetch('/api/create-zip', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({ version })
            })
            .then(response => {
                if (!response.ok) {
                    return response.json().then(err => { throw new Error(err.message) });
                }
                return response.json();
            })
            .then(data => {
                showSuccess(data.message);
            })
            .catch(error => {
                showError('Error: ' + error.message);
            });
        }

        // Show update version modal
        function showUpdateVersionModal() {
            document.getElementById('update-version-modal').style.display = 'block';
        }

        // Close update version modal
        function closeUpdateVersionModal() {
            document.getElementById('update-version-modal').style.display = 'none';
        }

        // Update version
        function updateVersion() {
            const versionType = document.querySelector('input[name="version-type"]:checked').value;
            const changeDescription = document.getElementById('change-description').value;

            const updateButton = document.querySelector('#update-version-modal .btn');
            updateButton.disabled = true;
            updateButton.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Updating...';

            fetch('/api/update-version', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({ versionType, changeDescription })
            })
            .then(response => {
                if (!response.ok) {
                    return response.json().then(err => { throw new Error(err.message) });
                }
                return response.json();
            })
            .then(data => {
                closeUpdateVersionModal();
                showSuccess(data.message);
                fetchProjectInfo();
                loadVersionHistory();
                document.getElementById('change-description').value = '';
            })
            .catch(error => {
                showError('Error: ' + error.message);
            })
            .finally(() => {
                updateButton.disabled = false;
                updateButton.innerHTML = '<i class="fas fa-save"></i> Update Version';
            });
        }

        // Message functions
        function showSuccess(message) {
            const successElement = document.getElementById('success-message');
            successElement.innerHTML = `<i class="fas fa-check-circle"></i> ${message}`;
            successElement.style.display = 'block';
            hideMessages(5000);
        }

        function showError(message) {
            const errorElement = document.getElementById('error-message');
            errorElement.innerHTML = `<i class="fas fa-exclamation-circle"></i> ${message}`;
            errorElement.style.display = 'block';
            hideMessages(5000);
        }

        function hideMessages(delay = 0) {
            setTimeout(() => {
                document.getElementById('success-message').style.display = 'none';
                document.getElementById('error-message').style.display = 'none';
            }, delay);
        }

        // Close modal when clicking outside
        window.onclick = function(event) {
            const modal = document.getElementById('update-version-modal');
            if (event.target === modal) {
                closeUpdateVersionModal();
            }
        }
    </script>

    <footer class="footer">
        <p>&copy; 2025 Project Versioner Pro - Created by Adrian Anesu Mupemhi for Benjamin Music Initiatives</p>
        <p>Professional project versioning made simple</p>
    </footer>
</body>
</html>
