<!DOCTYPE html>
<html>
<head>
  <title>Project Versioner</title>
  <style>
    @import url('https://fonts.googleapis.com/css2?family=Share+Tech+Mono&display=swap');
    
    :root {
      --matrix-dark: #0a0e0a;
      --matrix-green: #00ff41;
      --matrix-light-green: #7dffb3;
      --matrix-mid-green: #00bc3c;
      --matrix-dim-green: #003b00;
      --matrix-highlight: #48ff00;
      --matrix-text: #e0ffeb;
      --matrix-border: #00ff41;
      --matrix-shadow: 0 0 10px rgba(0, 255, 65, 0.5);
    }
    
    body { 
      font-family: 'Share Tech Mono', monospace;
      padding: 20px;
      max-width: 900px;
      margin: 0 auto;
      color: var(--matrix-text);
      background-color: var(--matrix-dark);
      background-image: 
        linear-gradient(rgba(0, 15, 2, 0.9), rgba(0, 15, 2, 0.9)),
        url("data:image/svg+xml,%3Csvg width='100' height='100' viewBox='0 0 100 100' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M11 18c3.866 0 7-3.134 7-7s-3.134-7-7-7-7 3.134-7 7 3.134 7 7 7zm48 25c3.866 0 7-3.134 7-7s-3.134-7-7-7-7 3.134-7 7 3.134 7 7 7zm-43-7c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zm63 31c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zM34 90c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zm56-76c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zM12 86c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm28-65c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm23-11c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm-6 60c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm29 22c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zM32 63c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm57-13c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm-9-21c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM60 91c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM35 41c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM12 60c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2z' fill='%2300ff41' fill-opacity='0.05' fill-rule='evenodd'/%3E%3C/svg%3E");
    }
    
    h1, h2, h3 { 
      color: var(--matrix-green);
      text-shadow: var(--matrix-shadow);
      letter-spacing: 1px;
    }
    
    h1 {
      border-bottom: 1px solid var(--matrix-border);
      padding-bottom: 10px;
      font-size: 2.2em;
      text-transform: uppercase;
    }
    
    .info-box {
      background-color: rgba(0, 20, 0, 0.7);
      border: 1px solid var(--matrix-border);
      border-radius: 4px;
      padding: 15px;
      margin-bottom: 20px;
      box-shadow: var(--matrix-shadow);
    }
    
    .info-item {
      margin-bottom: 12px;
    }
    
    .info-label {
      font-weight: bold;
      display: inline-block;
      width: 120px;
      color: var(--matrix-light-green);
    }
    
    .action-panel {
      display: flex;
      gap: 15px;
      margin-bottom: 20px;
    }
    
    button { 
      background-color: var(--matrix-dim-green);
      color: var(--matrix-green);
      border: 1px solid var(--matrix-border);
      padding: 10px 20px;
      font-size: 16px;
      cursor: pointer;
      border-radius: 4px;
      transition: all 0.3s;
      font-family: 'Share Tech Mono', monospace;
      text-transform: uppercase;
      letter-spacing: 1px;
      box-shadow: var(--matrix-shadow);
    }
    
    button:hover {
      background-color: var(--matrix-mid-green);
      color: var(--matrix-text);
      box-shadow: 0 0 15px rgba(0, 255, 65, 0.7);
      transform: translateY(-2px);
    }
    
    button:active {
      transform: translateY(1px);
    }
    
    button:disabled {
      background-color: rgba(0, 60, 0, 0.3);
      color: rgba(0, 255, 65, 0.4);
      cursor: not-allowed;
      box-shadow: none;
      transform: none;
    }
    
    .success-message {
      background-color: rgba(0, 50, 0, 0.7);
      color: var(--matrix-light-green);
      border: 1px solid var(--matrix-mid-green);
      border-radius: 4px;
      padding: 15px;
      margin-top: 20px;
      display: none;
      box-shadow: var(--matrix-shadow);
    }
    
    .error-message {
      background-color: rgba(50, 0, 0, 0.7);
      color: #ff6b6b;
      border: 1px solid #ff0000;
      border-radius: 4px;
      padding: 15px;
      margin-top: 20px;
      display: none;
    }
    
    footer {
      margin-top: 40px;
      padding-top: 10px;
      border-top: 1px solid var(--matrix-border);
      font-size: 12px;
      color: var(--matrix-mid-green);
      text-align: center;
    }
    
    .tabs {
      display: flex;
      border-bottom: 1px solid var(--matrix-border);
      margin-bottom: 20px;
    }
    
    .tab {
      padding: 10px 15px;
      cursor: pointer;
      border: 1px solid transparent;
      border-bottom: none;
      margin-bottom: -1px;
      color: var(--matrix-mid-green);
      transition: all 0.3s;
    }
    
    .tab:hover {
      color: var(--matrix-green);
    }
    
    .tab.active {
      border-color: var(--matrix-border);
      border-radius: 4px 4px 0 0;
      background-color: rgba(0, 20, 0, 0.7);
      border-bottom: 1px solid var(--matrix-dim-green);
      color: var(--matrix-green);
    }
    
    .tab-content {
      display: none;
    }
    
    .tab-content.active {
      display: block;
      animation: fadeIn 0.5s;
    }
    
    @keyframes fadeIn {
      from { opacity: 0; }
      to { opacity: 1; }
    }
    
    .version-list {
      list-style: none;
      padding: 0;
    }
    
    .version-item {
      padding: 12px;
      border-bottom: 1px solid var(--matrix-dim-green);
      cursor: pointer;
      transition: all 0.3s;
    }
    
    .version-item:hover {
      background-color: rgba(0, 255, 65, 0.1);
      transform: translateX(5px);
    }
    
    .version-date {
      color: var(--matrix-mid-green);
      font-size: 0.9em;
      margin-left: 10px;
    }
    
    .version-changes {
      white-space: pre-wrap;
      background-color: rgba(0, 20, 0, 0.7);
      padding: 15px;
      border-radius: 4px;
      margin-top: 10px;
      font-family: 'Share Tech Mono', monospace;
      border: 1px solid var(--matrix-dim-green);
      color: var(--matrix-light-green);
    }
    
    .modal {
      display: none;
      position: fixed;
      z-index: 1;
      left: 0;
      top: 0;
      width: 100%;
      height: 100%;
      overflow: auto;
      background-color: rgba(0, 10, 0, 0.8);
      backdrop-filter: blur(3px);
    }
    
    .modal-content {
      background-color: var(--matrix-dark);
      background-image: linear-gradient(rgba(0, 15, 2, 0.9), rgba(0, 15, 2, 0.9));
      margin: 10% auto;
      padding: 25px;
      border: 1px solid var(--matrix-border);
      width: 80%;
      max-width: 600px;
      border-radius: 4px;
      box-shadow: 0 0 20px rgba(0, 255, 65, 0.5);
      animation: modalOpen 0.4s;
    }
    
    @keyframes modalOpen {
      from { opacity: 0; transform: translateY(-50px); }
      to { opacity: 1; transform: translateY(0); }
    }
    
    .close {
      color: var(--matrix-mid-green);
      float: right;
      font-size: 28px;
      font-weight: bold;
      cursor: pointer;
      transition: all 0.3s;
    }
    
    .close:hover {
      color: var(--matrix-green);
      text-shadow: 0 0 10px var(--matrix-green);
    }
    
    textarea {
      width: 100%;
      height: 100px;
      padding: 10px;
      margin-bottom: 15px;
      border: 1px solid var(--matrix-dim-green);
      border-radius: 4px;
      resize: vertical;
      background-color: rgba(0, 20, 0, 0.7);
      color: var(--matrix-text);
      font-family: 'Share Tech Mono', monospace;
    }
    
    textarea:focus {
      outline: none;
      border-color: var(--matrix-green);
      box-shadow: 0 0 10px rgba(0, 255, 65, 0.3);
    }
    
    .version-type {
      margin-bottom: 20px;
    }
    
    .version-type label {
      margin-right: 15px;
      cursor: pointer;
      display: block;
      padding: 8px 0;
      transition: all 0.3s;
    }
    
    .version-type label:hover {
      color: var(--matrix-green);
    }
    
    input[type="radio"] {
      appearance: none;
      -webkit-appearance: none;
      width: 16px;
      height: 16px;
      border: 1px solid var(--matrix-mid-green);
      border-radius: 50%;
      outline: none;
      margin-right: 8px;
      position: relative;
      top: 2px;
      cursor: pointer;
    }
    
    input[type="radio"]:checked {
      border: 1px solid var(--matrix-green);
      background-color: transparent;
    }
    
    input[type="radio"]:checked::before {
      content: '';
      position: absolute;
      top: 3px;
      left: 3px;
      width: 8px;
      height: 8px;
      border-radius: 50%;
      background-color: var(--matrix-green);
      box-shadow: 0 0 5px var(--matrix-green);
    }
    
    /* Matrix digital rain effect in the background */
    .matrix-bg {
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      z-index: -1;
      opacity: 0.07;
      pointer-events: none;
    }
    
    /* Glowing effect for important elements */
    .glow {
      text-shadow: 0 0 5px var(--matrix-green);
    }
    
    /* Typing animation for headings */
    .typing-effect {
      overflow: hidden;
      border-right: 2px solid var(--matrix-green);
      white-space: nowrap;
      margin: 0 auto;
      animation: typing 3.5s steps(40, end), blink-caret 0.75s step-end infinite;
    }
    
    @keyframes typing {
      from { width: 0 }
      to { width: 100% }
    }
    
    @keyframes blink-caret {
      from, to { border-color: transparent }
      50% { border-color: var(--matrix-green) }
    }
  </style>
</head>
<body>
  <canvas id="matrix-bg" class="matrix-bg"></canvas>
  
  <h1 class="typing-effect">Project Versioner</h1>
  
  <div class="info-box">
    <div class="info-item">
      <span class="info-label">Project:</span>
      <span id="project-name" class="glow">Loading...</span>
    </div>
    <div class="info-item">
      <span class="info-label">Version:</span>
      <span id="project-version" class="glow">Loading...</span>
    </div>
    <div class="info-item">
      <span class="info-label">Description:</span>
      <span id="project-description">Loading...</span>
    </div>
  </div>
  
  <div class="tabs">
    <div class="tab active" data-tab="export">Export</div>
    <div class="tab" data-tab="versions">Version History</div>
  </div>
  
  <div id="export-tab" class="tab-content active">
    <div class="action-panel">
      <button id="export-button" onclick="exportVersion()">Export as ZIP</button>
      <button id="update-version-button" onclick="showUpdateVersionModal()">Update Version</button>
    </div>
    
    <div id="success-message" class="success-message"></div>
    <div id="error-message" class="error-message"></div>
  </div>
  
  <div id="versions-tab" class="tab-content">
    <h2>Version History</h2>
    <div id="version-history">
      <ul id="version-list" class="version-list">
        <li>Loading version history...</li>
      </ul>
    </div>
    
    <div id="version-details" style="display: none;">
      <h3>Changes in version <span id="selected-version" class="glow"></span></h3>
      <div id="version-changes" class="version-changes">
        Loading changes...
      </div>
    </div>
  </div>
  
  <!-- Update Version Modal -->
  <div id="update-version-modal" class="modal">
    <div class="modal-content">
      <span class="close" onclick="closeUpdateVersionModal()">&times;</span>
      <h2>Update Version</h2>
      
      <div class="version-type">
        <p>Current version: <span id="current-version-display" class="glow">Loading...</span></p>
        <p>Select version type:</p>
        <label>
          <input type="radio" name="version-type" value="major" id="major-version">
          Major (x.0.0) - Incompatible API changes
        </label>
        <label>
          <input type="radio" name="version-type" value="minor" id="minor-version">
          Minor (0.x.0) - Add functionality in a backward-compatible manner
        </label>
        <label>
          <input type="radio" name="version-type" value="patch" id="patch-version" checked>
          Patch (0.0.x) - Backward-compatible bug fixes
        </label>
      </div>
      
      <p>Describe the changes in this version:</p>
      <textarea id="change-description" placeholder="Describe what changed in this version..."></textarea>
      
      <button onclick="updateVersion()">Update Version</button>
    </div>
  </div>
  
  <footer>
    <p>Project Versioner &copy; 2025 - Curated by Adrian Anesu Mupemhi For Benjamin Music Initiatives</p>
  </footer>
  
  <script>
    // Matrix digital rain effect
    document.addEventListener('DOMContentLoaded', function() {
      const canvas = document.getElementById('matrix-bg');
      const ctx = canvas.getContext('2d');
      
      // Set canvas size
      canvas.width = window.innerWidth;
      canvas.height = window.innerHeight;
      
      // Characters to display
      const characters = '01アイウエオカキクケコサシスセソタチツテトナニヌネノハヒフヘホマミムメモヤユヨラリルレロワヲン';
      const fontSize = 14;
      const columns = canvas.width / fontSize;
      
      // Array to track the y position of each column
      const drops = [];
      for (let i = 0; i < columns; i++) {
        drops[i] = Math.random() * -100;
      }
      
      function draw() {
        // Semi-transparent black to create fade effect
        ctx.fillStyle = 'rgba(0, 5, 0, 0.05)';
        ctx.fillRect(0, 0, canvas.width, canvas.height);
        
        ctx.fillStyle = '#00ff41';
        ctx.font = fontSize + 'px monospace';
        
        // Draw characters
        for (let i = 0; i < drops.length; i++) {
          // Random character
          const text = characters.charAt(Math.floor(Math.random() * characters.length));
          
          // Draw the character
          ctx.fillText(text, i * fontSize, drops[i] * fontSize);
          
          // Reset if it's at the bottom of the screen or randomly
          if (drops[i] * fontSize > canvas.height && Math.random() > 0.975) {
            drops[i] = 0;
          }
          
          // Move the drop down
          drops[i]++;
        }
      }
      
      // Run the animation
      setInterval(draw, 35);
      
      // Resize canvas when window is resized
      window.addEventListener('resize', function() {
        canvas.width = window.innerWidth;
        canvas.height = window.innerHeight;
      });
    });
    
    // Fetch project info when the page loads
    window.addEventListener('DOMContentLoaded', () => {
      fetchProjectInfo();
      setupTabs();
      loadVersionHistory();
    });
    
    function setupTabs() {
      const tabs = document.querySelectorAll('.tab');
      tabs.forEach(tab => {
        tab.addEventListener('click', () => {
          // Remove active class from all tabs and content
          document.querySelectorAll('.tab').forEach(t => t.classList.remove('active'));
          document.querySelectorAll('.tab-content').forEach(c => c.classList.remove('active'));
          
          // Add active class to clicked tab and corresponding content
          tab.classList.add('active');
          const tabName = tab.getAttribute('data-tab');
          document.getElementById(`${tabName}-tab`).classList.add('active');
          
          // If versions tab is selected, load version history
          if (tabName === 'versions') {
            loadVersionHistory();
          }
        });
      });
    }
    
    function fetchProjectInfo() {
      fetch('/api/project-info')
        .then(response => {
          if (!response.ok) {
            throw new Error('Failed to fetch project info');
          }
          return response.json();
        })
        .then(data => {
          document.getElementById('project-name').textContent = data.name;
          document.getElementById('project-version').textContent = data.version;
          document.getElementById('current-version-display').textContent = data.version;
          document.getElementById('project-description').textContent = data.description || 'No description available';
        })
        .catch(error => {
          showError('Error loading project info: ' + error.message);
        });
    }
    
    function loadVersionHistory() {
      fetch('/api/version-history')
        .then(response => {
          if (!response.ok) {
            throw new Error('Failed to fetch version history');
          }
          return response.json();
        })
        .then(data => {
          const versionList = document.getElementById('version-list');
          
          if (data.versions && data.versions.length > 0) {
            versionList.innerHTML = '';
            
            data.versions.forEach(version => {
              const li = document.createElement('li');
              li.className = 'version-item';
              li.innerHTML = `Version <span class="glow">${version.version}</span> <span class="version-date">${version.date}</span>`;
              li.onclick = () => showVersionDetails(version.version);
              versionList.appendChild(li);
            });
          } else {
            versionList.innerHTML = '<li>No version history available</li>';
          }
        })
        .catch(error => {
          document.getElementById('version-list').innerHTML = `<li>Error loading version history: ${error.message}</li>`;
        });
    }
    
    function showVersionDetails(version) {
      document.getElementById('selected-version').textContent = version;
      document.getElementById('version-details').style.display = 'block';
      document.getElementById('version-changes').textContent = 'Loading changes...';
      
      fetch(`/api/version-changes/${version}`)
        .then(response => {
          if (!response.ok) {
            throw new Error('Failed to fetch version changes');
          }
          return response.json();
        })
        .then(data => {
          document.getElementById('version-changes').textContent = data.changes;
        })
        .catch(error => {
          document.getElementById('version-changes').textContent = `Error loading changes: ${error.message}`;
        });
    }
    
    function exportVersion() {
      const version = document.getElementById('project-version').textContent;
      
      // Disable the button during export
      const exportButton = document.getElementById('export-button');
      exportButton.disabled = true;
      exportButton.textContent = 'Creating ZIP...';
      
      // Hide any previous messages
      hideMessages();
      
      // Call our API endpoint
      fetch('/api/create-zip', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ version })
      })
      .then(response => {
        if (!response.ok) {
          return response.json().then(err => { throw new Error(err.message) });
        }
        return response.json();
      })
      .then(data => {
        showSuccess(data.message);
      })
      .catch(error => {
        showError('Error: ' + error.message);
      })
      .finally(() => {
        // Re-enable the button
        exportButton.disabled = false;
        exportButton.textContent = 'Export as ZIP';
      });
    }
    
    function showUpdateVersionModal() {
      document.getElementById('update-version-modal').style.display = 'block';
    }
    
    function closeUpdateVersionModal() {
      document.getElementById('update-version-modal').style.display = 'none';
    }
    
    function updateVersion() {
      // Get the selected version type
      const versionType = document.querySelector('input[name="version-type"]:checked').value;
      const changeDescription = document.getElementById('change-description').value;
      
      // Disable the button during update
      const updateButton = document.querySelector('#update-version-modal button');
      updateButton.disabled = true;
      updateButton.textContent = 'Updating...';
      
      // Call our API endpoint
      fetch('/api/update-version', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ versionType, changeDescription })
      })
      .then(response => {
        if (!response.ok) {
          return response.json().then(err => { throw new Error(err.message) });
        }
        return response.json();
      })
      .then(data => {
        closeUpdateVersionModal();
        showSuccess(data.message);
        fetchProjectInfo();
        loadVersionHistory();
      })
      .catch(error => {
        showError('Error: ' + error.message);
      })
      .finally(() => {
        // Re-enable the button
        updateButton.disabled = false;
        updateButton.textContent = 'Update Version';
      });
    }
    
    function showSuccess(message) {
      const successElement = document.getElementById('success-message');
      successElement.textContent = message;
      successElement.style.display = 'block';
    }
    
    function showError(message) {
      const errorElement = document.getElementById('error-message');
      errorElement.textContent = message;
      errorElement.style.display = 'block';
    }
    
    function hideMessages() {
      document.getElementById('success-message').style.display = 'none';
      document.getElementById('error-message').style.display = 'none';
    }
    
    // Close the modal when clicking outside of it
    window.onclick = function(event) {
      const modal = document.getElementById('update-version-modal');
      if (event.target == modal) {
        closeUpdateVersionModal();
      }
    }
  </script>
</body>
</html>
