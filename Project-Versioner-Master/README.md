# Project Versioner

A tool for creating versioned ZIP exports of Node.js projects with automatic version management and change tracking.

## Copyright

Copyright © 2025 - Curated by <PERSON> Benjamin Music Initiatives
All rights reserved.

## Features

- Creates ZIP archives of projects with proper versioning in the filename
- Provides a simple web interface for exporting project versions
- Automatically uses project name and version from package.json
- Excludes unnecessary files like node_modules and .git directories
- **Automatic version updates** with semantic versioning (major, minor, patch)
- **Change tracking** with automatic changelog generation
- **Version history** to easily compare different versions of your project

## Installation

### Option 1: Copy to Your Project

1. Copy this entire folder to your project
2. Navigate to your project directory
3. Install the package:
   ```bash
   npm install --save-dev ./Project-Versioner-Master
   ```
4. Add a script to your package.json:
   ```json
   "scripts": {
     "version-export": "project-versioner"
   }
   ```
5. Run the versioner:
   ```bash
   npm run version-export
   ```

### Option 2: Install Globally

1. Install the package globally:
   ```bash
   npm install -g ./Project-Versioner-Master
   ```
2. Navigate to any Node.js project
3. Run the versioner:
   ```bash
   project-versioner
   ```

## Usage

1. Start the versioner in your project directory
2. Open your browser to http://localhost:3001/export-version.html
3. Use the interface to:
   - Export your project as a ZIP file
   - Update the version number
   - Track changes between versions
   - View version history

## Version Management

The Project Versioner includes a comprehensive version management system:

### Updating Versions

1. Click the "Update Version" button
2. Select the version type:
   - **Major** (x.0.0): For incompatible API changes
   - **Minor** (0.x.0): For adding functionality in a backward-compatible manner
   - **Patch** (0.0.x): For backward-compatible bug fixes
3. Enter a description of the changes
4. Click "Update Version"

### Tracking Changes

The system automatically:
- Updates the version number in package.json
- Creates/updates a CHANGELOG.md file in your project
- Records all version updates with timestamps
- Includes your descriptions of changes
- Attempts to include Git commit messages if available

### Viewing Version History

1. Click the "Version History" tab
2. See a list of all previous versions with dates
3. Click on any version to see the changes made in that version

The ZIP file will be named according to your project's name and version in package.json.
For example, if your project is named "my-app" with version "1.2.3", the ZIP will be named "my-app-1.2.3.zip".

## Requirements

- Node.js 12 or higher
- A valid package.json file in your project
