#!/usr/bin/env node

/**
 * Universal Project Versioner Launcher
 * Works with any Node.js project
 * 
 * Copyright © 2025 - Curated by <PERSON> For Benjamin Music Initiatives
 */

const { spawn } = require('child_process');
const path = require('path');
const fs = require('fs');

// Get the directory where this script is located (Project-Versioner-Master)
const versionerDir = __dirname;

// Get the parent directory (should be the project directory)
const projectDir = path.dirname(versionerDir);

// Verify this is a valid project directory
const packageJsonPath = path.join(projectDir, 'package.json');
if (!fs.existsSync(packageJsonPath)) {
  console.error('Error: No package.json found in the project directory.');
  console.error('Project Directory:', projectDir);
  console.error('Please ensure this versioner is placed in a Node.js project.');
  process.exit(1);
}

// Read package.json to get project info
let projectInfo;
try {
  projectInfo = JSON.parse(fs.readFileSync(packageJsonPath, 'utf8'));
} catch (error) {
  console.error('Error reading package.json:', error.message);
  process.exit(1);
}

// Path to the Project Versioner
const versionerPath = path.join(versionerDir, 'bin', 'project-versioner.js');

// Verify the versioner exists
if (!fs.existsSync(versionerPath)) {
  console.error('Error: Project Versioner not found at:', versionerPath);
  process.exit(1);
}

console.log('🚀 Starting Project Versioner...');
console.log('📁 Project:', projectInfo.name || 'Unknown');
console.log('📦 Version:', projectInfo.version || 'Unknown');
console.log('📂 Directory:', projectDir);
console.log('');

// Change to the project directory and start the versioner
process.chdir(projectDir);

// Start the versioner process
const versioner = spawn('node', [versionerPath], {
  stdio: 'inherit',
  cwd: projectDir
});

// Handle process events
versioner.on('error', (error) => {
  console.error('Error starting Project Versioner:', error.message);
  process.exit(1);
});

versioner.on('close', (code) => {
  console.log(`Project Versioner exited with code ${code}`);
  process.exit(code);
});

// Handle Ctrl+C gracefully
process.on('SIGINT', () => {
  console.log('\n🛑 Shutting down Project Versioner...');
  versioner.kill('SIGINT');
});
