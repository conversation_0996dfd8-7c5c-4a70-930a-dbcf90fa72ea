// @flow
// Generated using flowgen2

import FileCache from './FileCache';
import MemoryCache from './MemoryCache';
import type {Callback} from './Callback';
import type {CachedResponse} from './CachedResponse';
import type {HttpVerb} from './HttpVerb';
import type {ICache} from './ICache';
import type {Options} from './Options';
const Response = require('http-response-object');
import {URL} from 'url';

declare function request(
  method: HttpVerb,
  url: string | URL,
  options: Options | null | void,
  callback: Callback,
): void | stream$Writable;

declare function request(
  method: HttpVerb,
  url: string | URL,
  callback: Callback,
): void | stream$Writable;

export default request;
export type {HttpVerb};
export type {Options};
export {FileCache};
export {MemoryCache};
export type {Callback};
export {Response};
export type {CachedResponse};
export type {ICache};
