// @flow
// Generated using flowgen2

import type {CachedResponse} from './CachedResponse';

interface ICache {
  getResponse(
    url: string,
    cb: (err: Error | null, response: CachedResponse | null) => void,
  ): void;
  setResponse(url: string, response: CachedResponse | null): void;
  updateResponseHeaders?: (
    url: string,
    response: {[key: 'headers' | 'requestTimestamp']: any},
  ) => void;
  invalidateResponse(url: string, cb: (err: Error | null) => void): void;
}

export type {ICache};
