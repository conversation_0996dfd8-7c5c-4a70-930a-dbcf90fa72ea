// @flow
// Generated using flowgen2

import type {CachedResponse} from './CachedResponse';
const Response = require('http-response-object');

type Policy = {maxage: number | null};
export type {Policy};

declare function isCacheable<T>(res: Response<T> | CachedResponse): boolean;
export {isCacheable};

declare function cachePolicy<T>(
  res: Response<T> | CachedResponse,
): Policy | null;
export {cachePolicy};
