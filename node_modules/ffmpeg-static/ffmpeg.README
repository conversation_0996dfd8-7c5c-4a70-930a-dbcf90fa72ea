{"name": "ffmpeg", "type": "release", "version": "6.0", "size": 78829164, "download": {"7z": {"url": "https://evermeet.cx/ffmpeg/ffmpeg-6.0.7z", "size": 16659077, "sig": "https://evermeet.cx/ffmpeg/ffmpeg-6.0.7z.sig"}, "zip": {"url": "https://evermeet.cx/ffmpeg/ffmpeg-6.0.zip", "size": 24461336, "sig": "https://evermeet.cx/ffmpeg/ffmpeg-6.0.zip.sig"}}, "libraries": {"internal": [{"name": "liba<PERSON><PERSON>", "version": "58.2.100"}, {"name": "libavcodec", "version": "60.3.100"}, {"name": "libavformat", "version": "60.3.100"}, {"name": "libavdevice", "version": "60.1.100"}, {"name": "libavfilter", "version": "9.3.100"}, {"name": "libswscale", "version": "7.1.100"}, {"name": "libswresample", "version": "4.10.100"}, {"name": "libpostproc", "version": "57.1.100"}], "external": [{"name": "aom", "version": "20220112-402e264b9 3gpp-2021-10-15-268", "url": "https://aomedia.googlesource.com/aom"}, {"name": "ass", "version": "0.17.0", "url": "https://github.com/libass/libass"}, {"name": "bluray", "version": "1.3.4", "url": "https://www.videolan.org/developers/libbluray.html"}, {"name": "bzip2", "version": "1.0.8", "url": "https://sourceware.org/bzip2/"}, {"name": "dav1d", "version": "20230227-ef0fb0b 1.1.0-4", "url": "https://code.videolan.org/videolan/dav1d"}, {"name": "expat", "version": "2.5.0", "url": "https://github.com/libexpat/libexpat"}, {"name": "faac", "version": "1.30", "url": "https://sourceforge.net/projects/faac/"}, {"name": "faad2", "version": "2.8.8", "url": "https://sourceforge.net/projects/faac/"}, {"name": "flac", "version": "1.3.3", "url": "https://xiph.org/flac/"}, {"name": "fontconfig", "version": "2.14.1", "url": "https://www.fontconfig.org"}, {"name": "freetype", "version": "2.12.1", "url": "https://www.freetype.org"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "version": "1.0.12", "url": "https://github.com/fribidi/fribidi"}, {"name": "gsm", "version": "1.0.22", "url": "http://www.quut.com/gsm/"}, {"name": "harfbuzz", "version": "3.2.0", "url": "https://github.com/harfbuzz/harfbuzz"}, {"name": "iconv", "version": "1.17", "url": "https://www.gnu.org/software/libiconv/"}, {"name": "lame", "version": "3.100", "url": "http://lame.sourceforge.net"}, {"name": "modplug", "version": "*******", "url": "http://modplug-xmms.sourceforge.net"}, {"name": "mysofa", "version": "20220516-9c419b6 1.2.1-18", "url": "https://github.com/hoene/libmysofa"}, {"name": "ogg", "version": "1.3.5", "url": "https://www.xiph.org/downloads/"}, {"name": "openal-soft", "version": "1.20.0", "url": "https://openal-soft.org"}, {"name": "opencore-amr", "version": "0.1.5", "url": "https://sourceforge.net/projects/opencore-amr/"}, {"name": "openh264", "version": "20230104-045aeac1 2.3.0-23", "url": "http://www.openh264.org"}, {"name": "openjpeg", "version": "20230207-ee58d770 2.5.0-25", "url": "https://github.com/uclouvain/openjpeg"}, {"name": "opus", "version": "1.3.1", "url": "http://www.opus-codec.org"}, {"name": "orc", "version": "0.4.32", "url": "https://gstreamer.freedesktop.org/src/orc/"}, {"name": "rubberband", "version": "3.1.2", "url": "https://breakfastquay.com/rubberband/"}, {"name": "shine", "version": "3.1.1", "url": "https://github.com/toots/shine"}, {"name": "snappy", "version": "20230112-30326e5 1.1.9-38", "url": "https://github.com/google/snappy"}, {"name": "soxr", "version": "0.1.3", "url": "https://sourceforge.net/projects/soxr/files/"}, {"name": "speex", "version": "1.2.0", "url": "https://www.speex.org"}, {"name": "theora", "version": "1.1.1", "url": "https://www.theora.org"}, {"name": "twolame", "version": "0.4.0", "url": "http://twolame.org"}, {"name": "uuid", "version": "1.6.2", "url": "http://www.ossp.org/pkg/lib/uuid/"}, {"name": "vid.stab", "version": "20220530-90c76ac 1.1.0-53", "url": "https://github.com/georgmartius/vid.stab"}, {"name": "vmaf", "version": "20230223-b0e3fa21 2.3.1-122", "url": "https://github.com/Netflix/vmaf"}, {"name": "vo-aacenc", "version": "0.1.3", "url": "https://sourceforge.net/projects/opencore-amr/"}, {"name": "vo-amrwbenc", "version": "0.1.3", "url": "https://sourceforge.net/projects/opencore-amr/"}, {"name": "vorbis", "version": "1.3.7", "url": "https://xiph.org/vorbis/"}, {"name": "vpx", "version": "20211108-888bafc78 1.11.0-30", "url": "https://github.com/webmproject/libvpx"}, {"name": "webp", "version": "20211105-8ea81561 1.2.1-31", "url": "https://github.com/webmproject/libwebp"}, {"name": "x264", "version": "20230128-eaa68fad 0.164.3106", "url": "https://www.videolan.org/developers/x264.html"}, {"name": "x265", "version": "20230222-38cf1c379 3.5+95-38cf1c379", "url": "https://bitbucket.org/multicoreware/x265_git/wiki/Home"}, {"name": "xavs", "version": "20110821-r55", "url": "http://xavs.sourceforge.net/"}, {"name": "xvidcore", "version": "1.3.7", "url": "https://labs.xvid.com/source/"}, {"name": "z.lib (zimg)", "version": "3.0.4", "url": "https://github.com/sekrit-twc/zimg"}, {"name": "zeromq", "version": "4.3.4", "url": "http://zeromq.org"}, {"name": "zlib", "version": "1.2.13", "url": "http://zlib.net"}, {"name": "zvbi", "version": "0.2.35", "url": "http://zapping.sourceforge.net/ZVBI/"}]}, "rssfeed": "https://evermeet.cx/ffmpeg/rss.xml"}