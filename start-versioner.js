#!/usr/bin/env node

/**
 * Project Versioner Launcher
 * Ensures the versioner always runs from the main project directory
 * 
 * Copyright © 2025 - Curated by <PERSON> For Benjamin Music Initiatives
 */

const { spawn } = require('child_process');
const path = require('path');
const fs = require('fs');

// Get the directory where this script is located (main project directory)
const projectDir = __dirname;

// Verify this is the correct project directory
const packageJsonPath = path.join(projectDir, 'package.json');
if (!fs.existsSync(packageJsonPath)) {
  console.error('Error: No package.json found in the project directory.');
  process.exit(1);
}

// Read package.json to verify it's the audio converter project
try {
  const packageJson = JSON.parse(fs.readFileSync(packageJsonPath, 'utf8'));
  if (packageJson.name !== 'chishandura-maungira') {
    console.error('Error: This launcher is for the chishandura-maungira project only.');
    process.exit(1);
  }
} catch (error) {
  console.error('Error reading package.json:', error.message);
  process.exit(1);
}

// Path to the Project Versioner
const versionerPath = path.join(projectDir, 'Project-Versioner-Master', 'bin', 'project-versioner.js');

// Verify the versioner exists
if (!fs.existsSync(versionerPath)) {
  console.error('Error: Project Versioner not found at:', versionerPath);
  console.error('Please ensure the Project-Versioner-Master folder is in the project directory.');
  process.exit(1);
}

console.log('🚀 Starting Project Versioner for chishandura-maungira...');
console.log('📁 Project Directory:', projectDir);
console.log('🔧 Versioner Path:', versionerPath);
console.log('');

// Change to the project directory and start the versioner
process.chdir(projectDir);

// Start the versioner process
const versioner = spawn('node', [versionerPath], {
  stdio: 'inherit',
  cwd: projectDir
});

// Handle process events
versioner.on('error', (error) => {
  console.error('Error starting Project Versioner:', error.message);
  process.exit(1);
});

versioner.on('close', (code) => {
  console.log(`Project Versioner exited with code ${code}`);
  process.exit(code);
});

// Handle Ctrl+C gracefully
process.on('SIGINT', () => {
  console.log('\n🛑 Shutting down Project Versioner...');
  versioner.kill('SIGINT');
});
